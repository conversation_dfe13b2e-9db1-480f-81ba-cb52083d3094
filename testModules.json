[{"name": "Job Module - sendEmailWhenRejectingJob", "testCases": [{"function": "sendEmailWhenRejectingJob", "caseSummary": "throw error if job not found", "expected": "throw message \"job.find.not_found\""}, {"function": "", "caseSummary": "throw error if email template not found", "expected": "throw message \"job.send_email.email_template_not_found\""}, {"function": "", "caseSummary": "send email successfully", "expected": "has properties:\n- service['emailService']"}]}]