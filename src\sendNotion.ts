import axios from 'axios';
import { TestCaseResult } from './utils/analyze-utils';

const token = '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';
const url = 'https://api.notion.com/v1/pages';
const pageId = '17d74a7a-c633-801e-ac5d-c29f5abc1f06';

function buildRowError(expected: string) {
  const rowError = expected.split('throw message ');

  if (rowError.length > 1) {
    return [
      { type: 'text', text: { content: 'throw message ' } },
      {
        type: 'text',
        text: { content: rowError[1] },
        annotations: { code: true },
      },
    ];
  }
  return [{ type: 'text', text: { content: expected } }];
}

function buildTableRows(rows: TestCaseResult[]) {
  const children: any[] = [];

  children.push({
    object: 'block',
    type: 'table_row',
    table_row: {
      cells: [
        [{ type: 'text', text: { content: 'Function' } }],
        [{ type: 'text', text: { content: 'Testcase summary' } }],
        [{ type: 'text', text: { content: 'Expected' } }],
      ],
    },
  });

  for (const row of rows) {
    children.push({
      object: 'block',
      type: 'table_row',
      table_row: {
        cells: [
          [{ type: 'text', text: { content: row.function } }],
          [{ type: 'text', text: { content: row.caseSummary } }],
          [...buildRowError(row.expected)],
        ],
      },
    });
  }

  return children;
}

export async function sendNotion(
  moduleName: string,
  testCase: TestCaseResult[],
) {
  const rowsTable = buildTableRows(testCase);

  // Notion giới hạn 100 rows per table, chia thành chunks
  const BATCH_SIZE = 99; // Để lại 1 row cho header
  const chunks: any[] = [];

  for (let i = 0; i < rowsTable.length; i += BATCH_SIZE) {
    chunks.push(rowsTable.slice(i, i + BATCH_SIZE));
  }

  // Gửi từng chunk
  for (let i = 0; i < chunks.length; i++) {
    const chunkTitle =
      chunks.length > 1
        ? `${moduleName} (Part ${i + 1}/${chunks.length})`
        : moduleName;

    const payload = {
      parent: {
        page_id: pageId,
      },
      properties: {
        title: [
          {
            text: {
              content: chunkTitle,
            },
          },
        ],
      },
      children: [
        {
          object: 'block',
          type: 'table',
          table: {
            table_width: 3,
            has_column_header: true,
            has_row_header: false,
            children: chunks[i],
          },
        },
      ],
    };

    try {
      const response = await axios.post(url, payload, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Notion-Version': '2022-06-28',
        },
      });

      console.log(`Sent chunk ${i + 1}/${chunks.length}:`, response.data);

      // Delay giữa các request để tránh rate limit
      if (i < chunks.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.error(`Error sending chunk ${i + 1}:`, error);
    }
  }
}
