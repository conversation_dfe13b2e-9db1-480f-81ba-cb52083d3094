import fs from 'fs';
import path, { dirname } from 'path';
import {
  SourceFile,
  SyntaxKind,
  CallExpression,
  ArrowFunction,
  Block,
  Node,
} from 'ts-morph';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export type TestCaseResult = {
  function: string;
  caseSummary: string;
  expected: string;
};

export function getTopDescribe(sourceFile: SourceFile) {
  const describeCalls = sourceFile
    .getDescendantsOfKind(SyntaxKind.CallExpression)
    .filter(
      (expr) =>
        (expr as CallExpression).getExpression().getText() === 'describe',
    );

  if (describeCalls.length === 0) return null;

  const topDescribe = describeCalls[0] as CallExpression;
  const rawName = topDescribe.getArguments()[0].getText().replace(/['"`]/g, '');
  const name = rawName.replace(/Service$/, '').trim() + ' Module';

  return { rawName, name };
}

export function getTestDescribeBlocks(
  sourceFile: SourceFile,
  topRawName: string,
) {
  const describeCalls = sourceFile
    .getDescendantsOfKind(SyntaxKind.CallExpression)
    .filter(
      (expr) =>
        (expr as CallExpression).getExpression().getText() === 'describe',
    );

  return describeCalls
    .map((expr) => expr as CallExpression)
    .filter(
      (expr) =>
        expr.getArguments()[0].getText().replace(/['"`]/g, '') !== topRawName,
    )
    .map((expr) => {
      const name = expr.getArguments()[0].getText().replace(/['"`]/g, '');
      const body = expr.getArguments()[1];
      return { name, body };
    });
}

export function extractTestCases(
  bodyNode: Node,
  functionName: string,
): TestCaseResult[] {
  const cases: TestCaseResult[] = [];
  const bodyFunc = bodyNode.asKind(SyntaxKind.ArrowFunction);
  if (!bodyFunc) return [];

  const block = bodyFunc.getBody().asKind(SyntaxKind.Block);
  if (!block) return [];

  const statements = block.getStatements();

  for (const stmt of statements) {
    const exprStmt = stmt.asKind(SyntaxKind.ExpressionStatement);
    if (!exprStmt) continue;

    const expr = exprStmt.getExpression().asKind(SyntaxKind.CallExpression);
    if (!expr || expr.getExpression().getText() !== 'it') continue;

    const itArgs = expr.getArguments();
    const rawCase = itArgs[0].getText().replace(/['"`]/g, '');
    const caseSummary = rawCase.replace(/^should\s+/i, '').trim();

    const testBlock = itArgs[1]
      .asKind(SyntaxKind.ArrowFunction)
      ?.getBody()
      .asKind(SyntaxKind.Block);
    if (!testBlock) continue;

    const expected = extractExpectations(testBlock);

    cases.push({
      function: functionName,
      caseSummary,
      expected,
    });
  }

  return cases;
}

function extractExpectations(block: Block): string {
  const expects = block
    .getDescendantsOfKind(SyntaxKind.CallExpression)
    .filter((call) => call.getExpression().getText().startsWith('expect'));

  const successPart = extractExpectedSuccess(expects);
  const errorPart = extractExpectedError(expects);

  return [successPart, errorPart].filter(Boolean).join('\n');
}

function extractExpectedSuccess(expects: CallExpression[]): string {
  const expected: string[] = [];
  const properties: Set<string> = new Set();

  for (const exp of expects) {
    const text = exp.getText();

    // Xử lý resolves.not.toThrow()
    if (text.includes('resolves.not.toThrow')) {
      expected.push("don't throw error and handle successfully");
      continue;
    }

    // Schema validation
    if (text.includes('[0]).toMatchSchema')) {
      expected.push('return list data match to schema');
      continue;
    } else if (text.includes('toMatchSchema')) {
      expected.push('return data match to schema');
      continue;
    } else if (text.includes('expect(result.docs)')) continue;

    // Bỏ qua các expect chứa function call
    if (text.includes('(service as any)') || text.includes('.resolves') || text.includes('.rejects')) {
      continue;
    }

    // Extract property paths from expect statements
    const expectPropertyMatch = text.match(/expect\(([^)]+)\)/);
    if (expectPropertyMatch) {
      const propertyPath = expectPropertyMatch[1];

      // Clean up property path
      const cleanPath = propertyPath
        .replace(/^(result|location|updatedLocation|createdLocation)\./, '')
        .replace(/\?\./, '.')
        .replace(/\.toString\(\)/, '')
        .replace(/\._id\.toString\(\)/, '')
        .replace(/\._id/, '');

      if (
        cleanPath &&
        !['result', 'location', 'updatedLocation', 'createdLocation'].includes(
          cleanPath,
        )
      ) {
        const propertyName = cleanPath.split('.')[0];
        properties.add(propertyName);
      }
    }

    // Handle toHaveProperty separately
    const propMatch = text.match(/toHaveProperty\(['"`](.*?)['"`]/);
    if (propMatch) {
      const propName = propMatch[1].split('.')[0];
      properties.add(propName);
    }
  }

  // Build result
  const result: string[] = [];

  if (expected.length > 0) {
    result.push(...expected);
  }

  if (properties.size > 0) {
    result.push(
      'has properties:\n' +
        Array.from(properties)
          .sort()
          .map((p) => `- ${p}`)
          .join('\n'),
    );
  }

  return result.join('\n');
}

function extractExpectedError(expects: CallExpression[]): string {
  const expected: string[] = [];

  const messageKeyRaw = fs.readFileSync(
    path.resolve(__dirname, '../../messageKey.json'),
    'utf-8',
  );
  const groupedMessageKeys = JSON.parse(messageKeyRaw) as Record<
    string,
    Record<string, string>
  >;

  // Flatten all groups
  const flatMessageKeys: Record<string, string> = {};
  function flattenKeys(obj: Record<string, any>, prefix: string = '') {
    Object.entries(obj).forEach(([key, value]) => {
      if (typeof value === 'object') {
        flattenKeys(value, `${prefix}${key}.`);
      } else {
        flatMessageKeys[`${prefix}${key}`] = value;
      }
    });
  }
  flattenKeys(groupedMessageKeys);

  for (const exp of expects) {
    const text = exp.getText();

    if (text.includes('rejects.toThrow')) {
      const argMatch = text.match(/toThrow\(([^)]*)\)/);
      const rawMsg = argMatch?.[1]?.trim();

      if (rawMsg) {
        const cleanedMsg = rawMsg.replace(/['"`\s,]/g, '').trim();
        const resolvedMessage = flatMessageKeys[cleanedMsg] || cleanedMsg;
        expected.push(`throw message "${resolvedMessage}"`);
      } else {
        expected.push('throw error');
      }
    }
  }

  return expected.join('\n');
}
