import path from 'path';
import { Project } from 'ts-morph';
import {
  TestCaseResult,
  getTopDescribe,
  getTestDescribeBlocks,
  extractTestCases,
} from './utils/analyze-utils';

type TestcaseModule = {
  name: string;
  testCases: TestCaseResult[];
};

export function analyzeTestFile(filePath: string, targetDescribe?: string): TestcaseModule[] {
  const project = new Project();
  const sourceFile = project.addSourceFileAtPath(path.resolve(filePath));

  const topDescribe = getTopDescribe(sourceFile);
  if (!topDescribe) return [];

  const moduleName = topDescribe.name;
  const describes = getTestDescribeBlocks(sourceFile, topDescribe.rawName);
  
  // Lọc theo targetDescribe nếu được chỉ định
  const filteredDescribes = targetDescribe 
    ? describes.filter(describe => describe.name === targetDescribe)
    : describes;

  const results: TestcaseModule[] = [];
  const testCaseResults: TestCaseResult[] = [];

  let previousFunctionName = '';

  for (const describe of filteredDescribes) {
    const testCases = extractTestCases(describe.body, describe.name);
    for (const testCase of testCases) {
      if (testCase.function === previousFunctionName) {
        testCase.function = '';
      } else {
        previousFunctionName = testCase.function;
      }
    }
    testCaseResults.push(...testCases);
  }

  // Cập nhật tên module nếu có targetDescribe
  const finalModuleName = targetDescribe 
    ? `${moduleName} - ${targetDescribe}`
    : moduleName;

  results.push({
    name: finalModuleName,
    testCases: testCaseResults,
  });

  return results;
}
