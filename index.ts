import axios from 'axios';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { analyzeTestFile } from './src/analyzeFile';
import fs from 'fs';
import { buildMessageKeys } from './src/buildMessageKey';
import { sendNotion } from './src/sendNotion';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const messageKeyFolder = path.resolve(
  '../EE-ACC-V2/ee-acc-v2-core/src/shared/message-keys',
);

const messageKeyData = buildMessageKeys(messageKeyFolder);
// console.log(messageKeyData);
fs.writeFileSync(
  path.resolve(__dirname, 'messageKey.json'),
  JSON.stringify(messageKeyData, null, 2),
  'utf-8',
);

const testFile = path.resolve(
  `../EE-ACC-V2/ee-acc-v2-core/src/modules/job/test/job.service.spec.ts`,
);

const targetDescribe = 'sendEmailWhenRejectingJob';

const testModules = analyzeTestFile(testFile, targetDescribe);
const testModulesString = JSON.stringify(testModules, null, 2);
fs.writeFileSync(
  path.resolve(__dirname, 'testModules.json'),
  testModulesString,
  'utf-8',
);

for (const testModule of testModules) {
  await sendNotion(testModule.name, testModule.testCases);
}
