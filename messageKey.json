{"ADDRESS_MESSAGE_KEYS": {"INVALID_COUNTRY": "address.form.invalid_country", "INVALID_REGION": "address.form.invalid_region"}, "CONTACT_MESSAGE_KEYS": {"FIND_CONTACT_NOT_FOUND": "contact.find.contact_not_found", "ORG_NOT_FOUND": "contact.find.org_not_found", "INVALID_ORG_TYPE": "contact.find.invalid_org_type", "INVALID_ROLE": "contact.form.invalid_role", "CONTACT_EXIST": "contact.form.contact_exist", "INVALID_COUNTRY": "contact.form.invalid_country", "INVALID_REGION": "contact.form.invalid_region", "EMAIL_EXISTED": "contact.form.email_existed", "UPDATE_CONTACT_TYPE_NOT_ALLOWED": "contact.update.contact_type_not_allowed", "SUPPLIER_CATEGORY_REQUIRED": "contact.form.supplier_category_required", "COUNTRY_NOT_FOUND": "contact.form.country_not_found", "WRONG_FORMAT_POSTAL_CODE": "contact.form.wrong_format_postal_code", "SUPPLIER_TYPE_RENTAL_MUST_NOT_HAVE_CATEGORY": "contact.form.supplier_type_rental_must_not_have_category", "SUPPLIER_TYPE_REGULAR_MUST_HAVE_CATEGORY": "contact.form.supplier_type_regular_must_have_category", "EXITED_ORGANIZATION_NAME": "contact.form.existed_organization_name", "RENTAL_SUPPLIER_CONTRACT_CANNOT_CHANGE": "contact.update.rental_supplier_contract_cannot_change"}, "CONTRACT_TYPE_MESSAGE_KEYS": {"NOT_FOUND": "contract_type.find.not_found"}, "CONTRACT_MESSAGE_KEYS": {"NOT_FOUND": "contract.find.not_found", "UNITS_ARE_NOT_PART_OF_LOCATION": "contract.form.units_are_not_part_of_location", "ROOT_UNIT_NOT_ALLOWED": "contract.form.root_unit_not_allowed", "INVALID_CUSTOMER_CONTACT": "contract.form.invalid_customer_contact", "INVALID_SUPPLIER_CONTACT": "contract.form.invalid_supplier_contact", "AGREEMENT_NOT_FOUND": "contract.form.agreement_not_found", "CONTRACT_ONLY_HAVE_ONE_ACCOMMODATION_AGREEMENTLINE": "contract.update.contract_only_have_one_accommodation_agreementline", "SOME_COSTLINE_GENERAL_ARE_NOT_IN_CONTRACT": "contract.update.some_costline_general_are_not_in_contract", "START_DATE_CANNOT_BE_CHANGED_BEFORE_60_DAYS": "contract.update.start_date_cannot_be_changed_before_60_days"}, "COSTLINE_MESSAGE_KEYS": {"NOT_FOUND": "costline.not_found", "CUSTOM_COSTLINE_INVALID_DEBTOR": "custom_costline.invalid_debtor", "CUSTOM_COSTLINE_LOCATION_OR_COSTCENTER_NOT_FOUND": "custom_costline.location_or_costcenter_not_found", "COST_LINES_MUST_HAVE_SAME_TYPE": "costline.cost_lines_must_have_same_type", "COUNTRY_NOT_FOUND": "costline.country_not_found", "BV_COMPANY_NOT_FOUND": "costline.bv_company_not_found"}, "COSTLINE_GENERAL_MESSAGE_KEYS": {"NOT_FOUND": "costlinegeneral.find.not_found", "CONTRACT_NOT_FOUND": "costlinegeneral.form.contract_not_found", "START_DATE_MUST_GREATER_THAN_START_DATE_OF_CONTRACT": "costlinegeneral.bulk_edit.start_date_must_greater_than_start_date_of_contract", "INVALID_START_DATE": "costlinegeneral.bulk_edit.invalid_start_date", "INVALID_END_DATE": "costlinegeneral.bulk_edit.invalid_end_date", "SOME_COSTLINE_GENERAL_ARE_NOT_IN_CONTRACT": "costlinegeneral.bulk_edit.some_costline_general_are_not_in_contract"}, "COSTTYPE_MESSAGE_KEYS": {"NOT_FOUND": "costtype.find.not_found", "NOT_JOB_OR_CUSTOM": "costtype.find.not_job_or_custom"}, "DOCUMENT_FILE_MESSAGE_KEYS": {"NOT_FOUND_ANY_UPLOAD_FILE": "document_file.create.not_found_any_upload_file", "CREATED_INVALID_TYPE": "document_file.create.invalid_type", "NOT_FOUND_DOCUMENT_FILE": "document_file.delete.not_found_document", "LOCATION_ONLY_FOR_TYPE_NIGTH_REGISTRATION": "document_file.create.location_only_for_type_night_registration", "DUPLICATE_UPLOAD_FILE": "document_file.create.duplicate_upload_file", "TYPE_NIGTH_REGISTRATION_MUST_HAVE_LOCATION": "document_file.create.type_night_registration_must_have_location"}, "EQUIPMENT_MESSAGE_KEYS": {"NOT_FOUND": "equipment.find.not_found", "EXISTS": "equipment.find.exists"}, "INVOICE_MESSAGE_KEYS": {"COST_LINES_MUST_HAVE_SAME_TYPE": "invoice.approve.cost_lines_must_have_same_type", "COST_LINES_NOT_FOUND": "invoice.approve.cost_lines_not_found", "COST_LINES_HAVE_APPROVED_IN_ANOTHER_INVOICE": "invoice.approve.cost_lines_have_approved_in_another_invoice", "COST_LINES_HAVE_TYPE_DIFFERENT_FROM_INVOICE": "invoice.approve.cost_lines_have_type_different_from_invoice", "APPROVED_INVOICE_NOT_FOUND": "invoice.get.approved_invoice_not_found"}, "JOB_TEMPLATE_MESSAGE_KEY": {"NOT_FOUND": "job_template.find.not_found", "EXISTED_IN_SYSTEM": "job_template.find.existed_in_system", "TYPE_GENERAL_DOES_NOT_HAVE_UNIT": "job_template.create.type_general_does_not_have_unit", "TYPE_INSPECTION_MUST_HAVE_UNIT": "job_template.create.type_inspection_must_have_unit", "TYPE_INSPECTION_CANNOT_CUSTOM_NAME": "job_template.create.type_inspection_cannot_custom_name", "UNIT_NOT_FOUND": "job_template.create.unit_not_found", "CANNOT_ADD_OR_REMOVE_POINTS": "job_template.update.cannot_add_or_remove_points", "POINT_NOT_FOUND": "job_template.delete.point_not_found", "POINTS_NOT_FOUND": "job_template.update.points_not_found", "UNITS_NOT_FOUND": "job_template.copy.units_not_found", "TYPE_GENERAL_MUST_HAVE_NAME": "job_template.create.type_general_must_have_name"}, "JOB_KEYS": {"PLATFORM_NOT_SUPPORT": "job.find.platform_not_support", "NOT_FOUND": "job.find.not_found", "REPORT_CONTACT_REQUIRED": "job.form.report_contact_required", "ASSIGNEE_OR_EMPLOYEEES_DOES_NOT_HAVE_PERMISSION": "job.form.assignee_or_employee_does_not_have_permission", "EMPLOYEE_MUST_HAVE_ASSIGNEE": "job.form.employee_must_have_assignee", "ONLY_ASSIGNEE_CAN_UPDATE_JOB": "job.update.only_assignee_can_update_job", "INSPECTION_JOB_MUST_HAVE_TYPE": "job.create.inspection_must_have_type", "MAINTENANCE_OR_CLEANER_JOB_ONLY_HAVE_TYPE_REGULAR_OR_PERIODIC": "job.create.maintenance_or_cleaner_job_only_have_type_regular_or_periodic", "UNITS_ARE_NO_PART_OF_LOCATION": "job.form.units_are_no_part_of_location", "NOT_FOUND_CONTACT": "job.form.contact_not_found", "EQUIPMENTS_NOT_FOUND": "job.form.equipments_not_found", "INVALID_IMAGES": "job.form.invalid_images", "LOCATION_NOT_FOUND": "job.create.location_not_found", "PLANNED_DATE_MUST_BE_GREATER_OR_EQUAL_THAN_NOW": "job.form.planned_date_must_be_greater_or_equal_than_now", "MUST_HAVE_ONE_UNIT_HAVE_POINTS": "job.form.must_have_one_unit_have_points", "PLANNER_DOES_NOT_HAVE_PERMISSION": "job.form.planner_does_not_have_permission", "MOBILE_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_OPEN_STATUS": "job.update.mobile_can_not_update_job_point_when_job_has_open_status", "PORTAL_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_STATUS_IN_PROGRESS": "job.update.portal_cannot_update_job_point_when_job_has_status_in_progress", "MISS_SOME_JOB_POINTS_OR_JOB_POINTS_NOT_FOUND": "job.update.miss_some_job_points_or_job_points_not_found", "JOB_POINTS_MISS_NOTES_OR_IMAGES": "job.update.job_points_miss_notes_or_images", "JOB_POINTS_MUST_HAVE_STATUS": "job.update.job_points_must_have_status", "MOBILE_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_READY_STATUS": "job.update.mobile_can_not_update_job_point_when_job_has_ready_status", "REVIEWER_DOES_NOT_HAVE_PERMISSION": "job.form.reviewer_does_not_have_permission", "JOBS_NOT_FOUND": "job.delete.jobs_not_found", "ONLY_DELETE_JOB_THAT_HAVE_STATUS_OPEN_OR_CLOSED": "job.delete.only_delete_job_that_have_status_open_or_closed", "API_ONLY_SUPPORT_FOR_MOBILE": "job.find.api_only_support_for_mobile", "JOB_STATUS_NOT_ALLOW_VIEW_SUMMARY": "job.view_summary.job_status_not_allow_view_summary", "JOB_STATUS_NOT_ALLOW_VIEW_DETAIL_JOB": "job.find.job_status_not_allow_view_detail_job", "JOB_STATUS_NOT_ALLOW_VIEW_REVIEW_JOB": "job.find.job_status_not_allow_view_review_job", "JOB_STATUS_NOT_ALLOW_UPDATE": "job.update.job_status_not_allow_update", "NOT_ALLOW_UPDATE_UNITS": "job.update.not_allow_update_unit", "UPDATE_STATUS_SUCCESS": "job.update_status.success", "UPDATE_STATUS_FAILED": "job.update_status.failed", "INVOICE_CONTACT_NOT_FOUND": "job.update_status.invoice_contact_not_found", "REPORT_TO_CONTACTS_NOT_FOUND": "job.update_status.report_to_contacts_not_found", "REPORT_TYPE_INTERNAL_DOES_NOT_HAVE_INVOICE_CONTACT": "job.update_status.report_type_internal_does_not_have_invoice_contact", "REPORT_TYPE_INTERNAL_HAVE_ONE_DEFAULT_REPORT_TO_CONTACT": "job.update_status.report_type_internal_have_one_default_report_to_contact", "EMPLOYEE_NOT_FOUND": "job.sync.employee_not_found", "ONLY_CO_WORKER_SYNC_DATA_FOR_JOB": "job.sync.only_co_worker_sync_data_for_job", "JOB_STATUS_NOT_ALLOW_SYNC_DATA": "job.sync.job_status_not_allow_sync_data", "CO_WORKER_CANNOT_UPDATE_JOB": "job.update.co_worker_cannot_update_job", "EMAIL_TEMPLATE_NOT_FOUND": "job.send_email.email_template_not_found", "CANNOT_CREATE_COSTLINE_FOR_JOB_HAVE_REPORT_TYPE_INTERNAL": "job.update.cannot_create_costline_for_job_have_report_type_internal", "COSTLINE_ARE_NOT_IN_JOB_POINT": "job.update.costline_are_not_in_job_point", "INVALID_COST_TYPE": "job.update.invalid_cost_type", "CANNOT_UPDATE_PLANNING_FOR_MULTIPLE_DAYS_JOB": "job.update.cannot_update_planning_for_multiple_days_job"}, "LOCATION_FILE_MESSAGE_KEYS": {"NOT_FOUND": "location.find.not_found"}, "LOCATION_MESSAGE_KEYS": {"NOT_FOUND": "location.find.not_found", "ADDRESS_EXISTED": "location.create.address_existed", "COST_CENTER_NOT_FOUND": "location.create.cost_center_not_found", "TEAM_NOT_FOUND": "location.create.team_not_found", "BV_COMPANY_NOT_FOUND": "location.form.bv_company_not_found", "BV_COMPANY_CONFLICT": "location.form.bv_company_conflict", "DUPLICATE_UNIT_ID": "location.form.duplicate_unit_id", "ROOT_UNIT_NOT_FOUND": "location.form.root_unit_not_found"}, "NIGHT_REGISTRATION_MESSAGE_KEYS": {"LOCATION_NOT_FOUND": "night_registration.location_not_found", "UNIT_NOT_FOUND": "night_registration.unit_not_found", "BED_NOT_FOUND": "night_registration.bed_not_found", "RESIDENT_NOT_FOUND": "night_registration.resident_not_found", "CONTACT_NOT_FOUND": "night_registration.contact_not_found", "RESERVATION_NOT_FOUND": "night_registration.reservation_not_found", "ONLY_DELETE_VIRTUAL_RESERVATION": "night_registration.only_delete_virtual_reservation", "BED_ALREADY_RESERVED": "night_registration.bed_already_reserved", "WARNING_CATEGORY_NOT_FOUND": "night_registration.warning_category_not_found", "RESIDENT_ALREADY_EXISTED": "night_registration.resident_already_existed", "WARNING_NOT_FOUND": "night_registration.warning_not_found", "SHEET_MUST_HAVE_DATA": "night_registration.import_resident.sheet_must_have_data", "INVALID_HEADERS": "night_registration.import_resident.invalid_headers", "INVALID_DATA": "night_registration.import_resident.invalid_data", "EMAIL_TEMPLATE_NOT_FOUND": "night_registration.email_template_not_found"}, "PDF_MESSAGE_KEYS": {"TENANT_NOT_FOUND": "pdf.create.tenant_not_found", "MISS_COMPANY_CONFIG": "pdf.create.miss_company_config", "GENERATE_PDF_FAILED": "pdf.create.generate_pdf_failed", "UPLOAD_PDF_TO_S3_FAILED": "pdf.create.upload_pdf_to_s3_failed"}, "ROLE_GROUP_MESSAGES_KEYS": {"NOT_FOUND": "role_group.not_found", "NAME_IS_EXIST": "role_group.name.exist", "SOME_ROLE_NOT_EXIST": "role_group.roles.some_role_not_exist"}, "TASK_MESSAGE_KEYS": {"NOT_FOUND": "task.find.not_found"}, "TEAM_MESSAGE_KEYS": {"NOT_FOUND": "team.find.not_found", "ALREADY_EXISTS": "team.find.exists", "EMPLOYEE_NOT_FOUND": "team.find.employee_not_found", "IS_EQUIPMENT": "team.find.is_equipment"}, "TENANT_USER_MESSAGE_KEYS": {"NOT_FOUND": "tenant_user.find.not_found", "USER_EXISTS": "tenant_user.find.exists", "EMAIL_EXISTS": "tenant_user.input.email_exists", "INVALID_ROLE": "tenant_user.input.invalid_role", "INVALID_TEAM": "tenant_user.input.invalid_team", "ROLES_REQUIRED": "tenant_user.input.roles_required", "WRONG_PASSWORD": "tenant_user.input.wrong_password", "OLD_PASSWORD": "tenant_user.input.old_password", "UPDATE_ROOT_USER": "tenant_user.update.root_user", "CHANGE_PASSWORD_BEFORE_SIX_MONTHS": "tenant_user.change_password.before_six_months", "USER_NOT_EXIST_IN_TEAM": "tenant_user.team_management.user_not_exist_in_team", "NOT_FOUND_DISPLAY_CONFIG": "tenant_user.display_config.find.not_found", "DISPLAY_CONFIG_NOT_MUTABLE": "tenant_user.display_config.update.not_mutable"}, "TENANT_MESSAGE_KEYS": {"NOT_FOUND": "tenant.find.not_found", "CONFIG_NOT_FOUND": "tenant.find.config_not_found"}}