import * as fs from 'fs';
import * as path from 'path';
import { Project, SyntaxKind } from 'ts-morph';

type GroupedMessageKeys = Record<string, Record<string, string>>;

export function buildMessageKeys(folderPath: string): GroupedMessageKeys {
  const project = new Project();
  const files = fs.readdirSync(folderPath).filter((f) => f.endsWith('.ts'));

  const grouped: GroupedMessageKeys = {};

  for (const file of files) {
    const filePath = path.join(folderPath, file);
    const sourceFile = project.addSourceFileAtPath(filePath);

    const exportedVariables = sourceFile
      .getVariableStatements()
      .filter((stmt) => stmt.isExported());

    for (const stmt of exportedVariables) {
      for (const decl of stmt.getDeclarations()) {
        const exportName = decl.getName(); // tên biến export
        const initializer = decl.getInitializerIfKind(
          SyntaxKind.ObjectLiteralExpression,
        );
        if (!initializer) continue;

        const keys: Record<string, string> = {};

        initializer.getProperties().forEach((prop) => {
          if (prop.getKind() === SyntaxKind.PropertyAssignment) {
            const property = prop.asKind(SyntaxKind.PropertyAssignment)!;
            const key = property.getName().replace(/['"`]/g, '');
            const val = property
              .getInitializer()
              ?.getText()
              .replace(/['"`]/g, '');
            if (key && val) keys[key] = val;
          }
        });

        if (Object.keys(keys).length > 0) {
          grouped[exportName] = keys;
        }
      }
    }
  }

  return grouped;
}
